<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Zero - Simple Test</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            background-color: var(--vscode-editor-background);
            color: var(--vscode-foreground);
            padding: 20px;
            margin: 0;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .title {
            font-size: 28px;
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 16px;
            opacity: 0.8;
        }
        .status-card {
            background: var(--vscode-textBlockQuote-background);
            border-left: 4px solid var(--vscode-terminal-ansiGreen);
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
        }
        .status-label {
            font-weight: bold;
        }
        .status-value {
            color: var(--vscode-terminal-ansiGreen);
        }
        .chat-area {
            background: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            min-height: 200px;
        }
        .input-area {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .input-field {
            flex: 1;
            background: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            color: var(--vscode-input-foreground);
            padding: 10px;
            border-radius: 3px;
        }
        .send-button {
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
        }
        .send-button:hover {
            background: var(--vscode-button-hoverBackground);
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: var(--vscode-textBlockQuote-background);
        }
        .user-message {
            text-align: right;
            background: var(--vscode-textLink-foreground);
            color: white;
        }
        .bot-message {
            text-align: left;
            background: var(--vscode-textBlockQuote-background);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">🤖 Agent Zero</div>
            <div class="subtitle">Simple WebView Test - Working!</div>
        </div>

        <div class="status-card">
            <h3>📊 Status Check</h3>
            <div class="status-item">
                <span class="status-label">WebView:</span>
                <span class="status-value" id="webview-status">✅ Active</span>
            </div>
            <div class="status-item">
                <span class="status-label">VSCode API:</span>
                <span class="status-value" id="vscode-api-status">⏳ Checking...</span>
            </div>
            <div class="status-item">
                <span class="status-label">JavaScript:</span>
                <span class="status-value" id="js-status">⏳ Checking...</span>
            </div>
            <div class="status-item">
                <span class="status-label">Timestamp:</span>
                <span class="status-value" id="timestamp">⏳ Loading...</span>
            </div>
        </div>

        <div class="chat-area" id="chat-area">
            <div class="message bot-message">
                👋 مرحباً! هذا اختبار بسيط للـ webview. إذا كنت تستطيع رؤية هذه الرسالة، فإن الـ webview يعمل بشكل صحيح!
            </div>
        </div>

        <div class="input-area">
            <input type="text" class="input-field" id="message-input" placeholder="اكتب رسالة للاختبار...">
            <button class="send-button" onclick="sendMessage()">إرسال</button>
        </div>
    </div>

    <script>
        console.log('🚀 Simple Agent Zero WebView loaded!');
        
        // Check VSCode API
        let vscode = null;
        try {
            if (typeof acquireVsCodeApi !== 'undefined') {
                vscode = acquireVsCodeApi();
                document.getElementById('vscode-api-status').textContent = '✅ Available';
                console.log('✅ VSCode API acquired successfully');
            } else {
                document.getElementById('vscode-api-status').textContent = '❌ Not Available';
                console.warn('❌ VSCode API not available');
            }
        } catch (error) {
            document.getElementById('vscode-api-status').textContent = '❌ Error';
            console.error('❌ Error acquiring VSCode API:', error);
        }

        // Check JavaScript
        document.getElementById('js-status').textContent = '✅ Working';
        
        // Set timestamp
        document.getElementById('timestamp').textContent = new Date().toLocaleString('ar-EG');

        // Simple chat functionality
        function sendMessage() {
            const input = document.getElementById('message-input');
            const chatArea = document.getElementById('chat-area');
            
            if (input.value.trim()) {
                // Add user message
                const userMsg = document.createElement('div');
                userMsg.className = 'message user-message';
                userMsg.textContent = input.value;
                chatArea.appendChild(userMsg);
                
                // Add bot response
                setTimeout(() => {
                    const botMsg = document.createElement('div');
                    botMsg.className = 'message bot-message';
                    botMsg.textContent = `🤖 تم استلام رسالتك: "${input.value}" - الـ webview يعمل بشكل ممتاز!`;
                    chatArea.appendChild(botMsg);
                    chatArea.scrollTop = chatArea.scrollHeight;
                }, 500);
                
                input.value = '';
                chatArea.scrollTop = chatArea.scrollHeight;
                
                // Send to VSCode if available
                if (vscode) {
                    vscode.postMessage({
                        command: 'test-message',
                        text: input.value
                    });
                }
            }
        }

        // Handle Enter key
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Listen for messages from VSCode
        if (vscode) {
            window.addEventListener('message', event => {
                const message = event.data;
                console.log('📨 Received message from VSCode:', message);
                
                if (message.command === 'response') {
                    const chatArea = document.getElementById('chat-area');
                    const botMsg = document.createElement('div');
                    botMsg.className = 'message bot-message';
                    botMsg.textContent = `📨 من VSCode: ${message.text}`;
                    chatArea.appendChild(botMsg);
                    chatArea.scrollTop = chatArea.scrollHeight;
                }
            });
        }

        console.log('✅ Simple WebView initialization complete');
    </script>
</body>
</html>
