import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import * as crypto from 'crypto';

export class OriginalWebUIProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'agent-zero-chat';
    private _view?: vscode.WebviewView;
    private _nonce: string;

    constructor(private readonly context: vscode.ExtensionContext) {
        this._nonce = this.generateNonce();
    }

    private generateNonce(): string {
        return crypto.randomBytes(16).toString('base64');
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        console.log('OriginalWebUIProvider: resolveWebviewView called');
        this._view = webviewView;

        // Generate a new nonce for each webview load
        this._nonce = this.generateNonce();

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                vscode.Uri.file(path.join(this.context.extensionPath, 'media')),
                vscode.Uri.file(path.join(this.context.extensionPath, 'out')),
                this.context.extensionUri
            ]
        };

        console.log('OriginalWebUIProvider: Setting webview HTML');
        console.log('OriginalWebUIProvider: Extension path:', this.context.extensionPath);
        console.log('OriginalWebUIProvider: Media path:', path.join(this.context.extensionPath, 'media'));

        // First try to load a simple test HTML to see if webview works at all
        try {
            const indexPath = path.join(this.context.extensionPath, 'media', 'index.html');
            if (fs.existsSync(indexPath)) {
                console.log('OriginalWebUIProvider: index.html found, attempting to load...');
                webviewView.webview.html = this.getOriginalWebUI(webviewView.webview);
                console.log('OriginalWebUIProvider: Original WebUI HTML set successfully');
            } else {
                console.log('OriginalWebUIProvider: index.html not found, showing test HTML');
                webviewView.webview.html = this.getTestHtml();
            }
        } catch (error) {
            console.error('OriginalWebUIProvider: Error setting HTML:', error);
            webviewView.webview.html = this.getErrorHtml(`Failed to load webview: ${error}`);
        }
    }

    public show(): void {
        if (this._view) {
            this._view.show?.(true);
        }
    }

    public refresh(): void {
        if (this._view) {
            // Generate new nonce for security
            this._nonce = this.generateNonce();

            try {
                this._view.webview.html = this.getOriginalWebUI(this._view.webview);
                console.log('OriginalWebUIProvider: Webview refreshed successfully');
            } catch (error) {
                console.error('OriginalWebUIProvider: Error refreshing webview:', error);
                this._view.webview.html = this.getErrorHtml(`Failed to refresh webview: ${error}`);
            }
        }
    }

    public dispose(): void {
        this._view = undefined;
    }

    private getOriginalWebUI(webview: vscode.Webview): string {
        try {
            // Path to the media folder (where webui files are copied)
            const webuiPath = path.join(this.context.extensionPath, 'media');
            const indexPath = path.join(webuiPath, 'index.html');
            const simpleTestPath = path.join(webuiPath, 'simple-test.html');

            console.log('OriginalWebUIProvider: Checking media path:', webuiPath);
            console.log('OriginalWebUIProvider: Index path:', indexPath);
            console.log('OriginalWebUIProvider: Index exists:', fs.existsSync(indexPath));
            console.log('OriginalWebUIProvider: Simple test exists:', fs.existsSync(simpleTestPath));

            // Always load the simple test first until we fix the main issue
            console.log('OriginalWebUIProvider: Loading simple test HTML...');
            return this.getSimpleWorkingHtml();

            // Check if media files exist
            if (!fs.existsSync(indexPath)) {
                console.error('OriginalWebUIProvider: WebUI index.html not found in media folder');
                return this.getErrorHtml('WebUI files not found in media folder. Please run "npm run copy-assets" to copy webui files.');
            }

            // Read the original index.html
            let html = fs.readFileSync(indexPath, 'utf8');
            console.log('OriginalWebUIProvider: HTML file read, length:', html.length);

            // Convert relative paths to webview URIs and add CSP
            html = this.processHtmlForWebview(html, webview, webuiPath);
            console.log('OriginalWebUIProvider: HTML processed, final length:', html.length);

            return html;
        } catch (error) {
            console.error('OriginalWebUIProvider: Error loading original webui:', error);
            return this.getErrorHtml(`Error loading webui: ${error}`);
        }
    }

    private processHtmlForWebview(html: string, webview: vscode.Webview, webuiPath: string): string {
        // More permissive CSP for debugging
        const cspMeta = `<meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline' https:; script-src ${webview.cspSource} 'unsafe-inline' 'unsafe-eval' https:; font-src ${webview.cspSource} https: data:; img-src ${webview.cspSource} data: https: blob:; connect-src ${webview.cspSource} https: wss: ws:;">`;

        // Insert CSP after the charset meta tag
        html = html.replace(/<meta charset="UTF-8">/, `<meta charset="UTF-8">\n    ${cspMeta}`);

        // Convert relative paths to webview URIs
        html = this.convertPathsToWebviewUris(html, webview, webuiPath);

        // Add debug script at the end
        const debugScript = `
        <script>
            console.log('🚀 Agent Zero WebView loaded successfully!');
            console.log('📍 Current location:', window.location.href);
            console.log('🔧 VSCode API available:', typeof acquireVsCodeApi !== 'undefined');

            // Check if all CSS files loaded
            const links = document.querySelectorAll('link[rel="stylesheet"]');
            console.log('📄 CSS files found:', links.length);

            // Check if all JS files loaded
            const scripts = document.querySelectorAll('script[src]');
            console.log('📜 JS files found:', scripts.length);

            // Log any errors
            window.addEventListener('error', function(e) {
                console.error('❌ Error:', e.error, 'at', e.filename + ':' + e.lineno);
            });

            // Log resource loading errors
            window.addEventListener('load', function() {
                console.log('✅ Page fully loaded');
            });
        </script>`;

        html = html.replace('</body>', debugScript + '\n</body>');

        return html;
    }

    private getTestHtml(): string {
        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'nonce-${this._nonce}';">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Agent Zero Test</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        background-color: var(--vscode-editor-background);
                        color: var(--vscode-foreground);
                        padding: 20px;
                        margin: 0;
                        line-height: 1.6;
                    }
                    .container {
                        max-width: 500px;
                        margin: 0 auto;
                        text-align: center;
                        padding: 40px 20px;
                    }
                    .title {
                        font-size: 28px;
                        font-weight: bold;
                        margin-bottom: 20px;
                        color: var(--vscode-textLink-foreground);
                    }
                    .message {
                        font-size: 16px;
                        line-height: 1.5;
                        margin-bottom: 20px;
                        padding: 15px;
                        border-radius: 5px;
                    }
                    .success {
                        color: var(--vscode-terminal-ansiGreen);
                        font-weight: bold;
                        background: var(--vscode-textBlockQuote-background);
                        border-left: 4px solid var(--vscode-terminal-ansiGreen);
                    }
                    .info {
                        background: var(--vscode-textBlockQuote-background);
                        border-left: 4px solid var(--vscode-textLink-foreground);
                    }
                    .status-indicator {
                        display: inline-block;
                        width: 12px;
                        height: 12px;
                        border-radius: 50%;
                        background: var(--vscode-terminal-ansiGreen);
                        margin-right: 8px;
                        animation: pulse 2s infinite;
                    }
                    @keyframes pulse {
                        0% { opacity: 1; }
                        50% { opacity: 0.5; }
                        100% { opacity: 1; }
                    }
                    .test-details {
                        text-align: left;
                        margin-top: 30px;
                        font-size: 14px;
                        opacity: 0.8;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="title">🤖 Agent Zero WebUI</div>
                    <div class="message success">
                        <span class="status-indicator"></span>
                        ✅ Webview is working correctly!
                    </div>
                    <div class="message info">
                        This is a test page to verify that the webview provider is functioning correctly.
                    </div>
                    <div class="message info">
                        If you can see this page with proper styling, the sidebar webview is properly configured.
                    </div>
                    <div class="test-details">
                        <strong>Test Results:</strong><br>
                        • Webview Provider: ✅ Active<br>
                        • HTML Rendering: ✅ Working<br>
                        • CSS Styling: ✅ Applied<br>
                        • VSCode Integration: ✅ Connected<br>
                        • Timestamp: ${new Date().toLocaleString()}
                    </div>
                </div>
                <script nonce="${this._nonce}">
                    console.log('Agent Zero Test WebView loaded successfully');
                    console.log('Timestamp:', new Date().toISOString());

                    // Test VSCode API availability
                    if (typeof acquireVsCodeApi !== 'undefined') {
                        console.log('VSCode API is available');
                        const vscode = acquireVsCodeApi();
                        console.log('VSCode API acquired successfully');
                    } else {
                        console.warn('VSCode API is not available');
                    }
                </script>
            </body>
            </html>
        `;
    }

    private convertPathsToWebviewUris(html: string, webview: vscode.Webview, webuiPath: string): string {
        // List of patterns to convert
        const patterns = [
            // CSS files
            { regex: /href="([^"]+\.css)"/g, attr: 'href' },
            // JS files
            { regex: /src="([^"]+\.js)"/g, attr: 'src' },
            // Images and icons
            { regex: /(src|href)="([^"]+\.(png|jpg|jpeg|gif|svg|ico|ttf|woff|woff2))"/g, attr: null },
            // Vendor files
            { regex: /(src|href)="(vendor\/[^"]+)"/g, attr: null },
            // Public folder assets
            { regex: /(src|href)="(public\/[^"]+)"/g, attr: null },
            // Components folder
            { regex: /(src|href)="(components\/[^"]+)"/g, attr: null },
            // JS folder files
            { regex: /(src|href)="(js\/[^"]+)"/g, attr: null },
            // CSS folder files
            { regex: /(src|href)="(css\/[^"]+)"/g, attr: null }
        ];

        for (const pattern of patterns) {
            html = html.replace(pattern.regex, (match, ...args) => {
                let attr: string;
                let filePath: string;

                if (pattern.attr) {
                    // Simple pattern with single capture group
                    attr = pattern.attr;
                    filePath = args[0];
                } else {
                    // Complex pattern with multiple capture groups
                    attr = args[0];
                    filePath = args[1];
                }

                // Skip external URLs
                if (filePath.startsWith('http') || filePath.startsWith('//')) {
                    return match;
                }

                // Handle relative paths starting with ./
                if (filePath.startsWith('./')) {
                    filePath = filePath.substring(2);
                }

                const fullPath = path.join(webuiPath, filePath);

                try {
                    if (fs.existsSync(fullPath)) {
                        const uri = webview.asWebviewUri(vscode.Uri.file(fullPath));
                        return `${attr}="${uri}"`;
                    } else {
                        console.warn(`OriginalWebUIProvider: File not found: ${fullPath}`);
                    }
                } catch (error) {
                    console.error(`OriginalWebUIProvider: Error processing path ${fullPath}:`, error);
                }

                return match;
            });
        }

        return html;
    }

    private getErrorHtml(errorMessage: string): string {
        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'nonce-${this._nonce}';">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Agent Zero WebUI Error</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        background-color: var(--vscode-editor-background);
                        color: var(--vscode-foreground);
                        padding: 20px;
                        margin: 0;
                        line-height: 1.6;
                    }
                    .error-container {
                        max-width: 600px;
                        margin: 0 auto;
                        text-align: center;
                        padding: 40px 20px;
                    }
                    .error-icon {
                        font-size: 48px;
                        margin-bottom: 20px;
                    }
                    .error-title {
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 10px;
                        color: var(--vscode-errorForeground);
                    }
                    .error-message {
                        font-size: 16px;
                        line-height: 1.5;
                        margin-bottom: 30px;
                        opacity: 0.8;
                        background: var(--vscode-textBlockQuote-background);
                        padding: 15px;
                        border-radius: 5px;
                        border-left: 4px solid var(--vscode-errorForeground);
                    }
                    .error-help {
                        font-size: 14px;
                        opacity: 0.8;
                        border-top: 1px solid var(--vscode-panel-border);
                        padding-top: 20px;
                        margin-top: 20px;
                        text-align: left;
                    }
                    .error-help h3 {
                        color: var(--vscode-textLink-foreground);
                        margin-bottom: 15px;
                    }
                    .error-help pre {
                        background: var(--vscode-textCodeBlock-background);
                        padding: 10px;
                        border-radius: 3px;
                        overflow-x: auto;
                        font-family: var(--vscode-editor-font-family);
                    }
                    .error-help ul {
                        padding-left: 20px;
                    }
                    .error-help li {
                        margin-bottom: 8px;
                    }
                    .retry-button {
                        background: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        padding: 10px 20px;
                        border-radius: 3px;
                        cursor: pointer;
                        margin-top: 20px;
                    }
                    .retry-button:hover {
                        background: var(--vscode-button-hoverBackground);
                    }
                </style>
            </head>
            <body>
                <div class="error-container">
                    <div class="error-icon">⚠️</div>
                    <div class="error-title">Agent Zero WebUI Loading Error</div>
                    <div class="error-message">${errorMessage}</div>
                    <div class="error-help">
                        <h3>🔧 Troubleshooting Steps:</h3>
                        <ul>
                            <li>Make sure to run the copy assets script to copy webui files to the media folder</li>
                            <li>Check that the webui folder exists in the parent directory</li>
                            <li>Verify that all required files are present in the media folder</li>
                        </ul>

                        <h3>📝 Run this command:</h3>
                        <pre>npm run copy-assets</pre>

                        <h3>📁 Expected structure:</h3>
                        <pre>vscode-extension/
├── media/
│   ├── index.html
│   ├── index.js
│   ├── index.css
│   ├── css/
│   ├── js/
│   ├── public/
│   └── vendor/
└── src/
    └── ...</pre>

                        <button class="retry-button" onclick="location.reload()">🔄 Retry Loading</button>
                    </div>
                </div>
                <script nonce="${this._nonce}">
                    console.log('Agent Zero WebUI Error Page Loaded');
                    console.error('WebUI Error:', '${errorMessage.replace(/'/g, "\\'")}');
                </script>
            </body>
            </html>
        `;
    }

    private getSimpleWorkingHtml(): string {
        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'unsafe-inline';">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Zero</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: var(--vscode-font-family, 'Segoe UI', sans-serif);
            background: var(--vscode-editor-background, #1e1e1e);
            color: var(--vscode-foreground, #cccccc);
            height: 100vh; overflow: hidden; direction: rtl;
        }
        .container { display: flex; flex-direction: column; height: 100vh; }
        .header {
            background: var(--vscode-titleBar-activeBackground, #3c3c3c);
            padding: 15px 20px; border-bottom: 1px solid var(--vscode-panel-border, #2d2d30);
            text-align: center;
        }
        .title { font-size: 18px; font-weight: bold; margin-bottom: 5px; }
        .subtitle { font-size: 12px; opacity: 0.7; }
        .chat-container { flex: 1; display: flex; flex-direction: column; overflow: hidden; }
        .messages { flex: 1; padding: 20px; overflow-y: auto; }
        .message {
            margin-bottom: 15px; padding: 12px 16px; border-radius: 8px;
            max-width: 85%; word-wrap: break-word;
        }
        .user-message {
            background: var(--vscode-button-background, #0e639c);
            color: var(--vscode-button-foreground, #ffffff);
            margin-left: auto; margin-right: 0; text-align: right;
        }
        .bot-message {
            background: var(--vscode-textBlockQuote-background, #2d2d30);
            margin-right: auto; margin-left: 0;
            border-left: 3px solid var(--vscode-textLink-foreground, #3794ff);
        }
        .input-area {
            background: var(--vscode-input-background, #3c3c3c);
            border-top: 1px solid var(--vscode-panel-border, #2d2d30);
            padding: 15px 20px; display: flex; gap: 10px; align-items: center;
        }
        .input-field {
            flex: 1; background: var(--vscode-input-background, #3c3c3c);
            border: 1px solid var(--vscode-input-border, #464647);
            color: var(--vscode-input-foreground, #cccccc);
            padding: 10px 12px; border-radius: 4px; outline: none; direction: rtl;
        }
        .input-field:focus { border-color: var(--vscode-focusBorder, #007acc); }
        .send-button {
            background: var(--vscode-button-background, #0e639c);
            color: var(--vscode-button-foreground, #ffffff);
            border: none; padding: 10px 16px; border-radius: 4px; cursor: pointer;
        }
        .send-button:hover { background: var(--vscode-button-hoverBackground, #1177bb); }
        .welcome { text-align: center; padding: 40px 20px; opacity: 0.7; }
        .welcome-icon { font-size: 48px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">🤖 Agent Zero</div>
            <div class="subtitle">مساعد الذكي للبرمجة</div>
        </div>
        <div class="chat-container">
            <div class="messages" id="messages">
                <div class="welcome">
                    <div class="welcome-icon">🚀</div>
                    <h3>مرحباً بك في Agent Zero!</h3>
                    <p>أنا مساعدك الذكي للبرمجة. كيف يمكنني مساعدتك اليوم؟</p>
                </div>
            </div>
            <div class="input-area">
                <input type="text" class="input-field" id="messageInput" placeholder="اكتب رسالتك هنا...">
                <button class="send-button" onclick="sendMessage()">إرسال</button>
            </div>
        </div>
    </div>
    <script>
        console.log('🚀 Agent Zero WebView loaded!');
        let vscode;
        try { vscode = acquireVsCodeApi(); } catch(e) { console.warn('VSCode API not available'); }

        function addMessage(content, isUser = false) {
            const div = document.createElement('div');
            div.className = 'message ' + (isUser ? 'user-message' : 'bot-message');
            div.textContent = content;
            const welcome = document.querySelector('.welcome');
            if (welcome) welcome.remove();
            document.getElementById('messages').appendChild(div);
            document.getElementById('messages').scrollTop = 999999;
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const msg = input.value.trim();
            if (!msg) return;
            addMessage(msg, true);
            input.value = '';
            if (vscode) vscode.postMessage({command: 'sendMessage', text: msg});
            setTimeout(() => addMessage('تم استلام رسالتك: "' + msg + '". شكراً! 🤖'), 1000);
        }

        document.getElementById('messageInput').addEventListener('keypress', e => {
            if (e.key === 'Enter') sendMessage();
        });

        if (vscode) {
            window.addEventListener('message', event => {
                if (event.data.command === 'response') addMessage(event.data.text);
            });
        }
    </script>
</body>
</html>`;
    }
}
